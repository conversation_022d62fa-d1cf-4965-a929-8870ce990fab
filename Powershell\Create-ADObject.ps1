<#
.SYNOPSIS
    Creates an AD Computer Account from the AutoDeploy service.

.DESCRIPTION
    Creates an AD Computer Account from the AutoDeploy service, for Windows, Clusters and VDI virtual machines.

.PARAMETER JobID
    The ID of the job.

.PARAMETER objectName
    The name of the virtual machine or cluster.

.PARAMETER objectDescription
    The description of the virtual machine or cluster.

.PARAMETER vmOS
    The operating system of the virtual machine.

.PARAMETER Domain
    The domain suffix for the AD object.

.PARAMETER ouPath
    The organizational unit path for the AD object.

.PARAMETER appType
    The type of application.

.PARAMETER clusterNodes
    Comma-separated list of cluster nodes for CLS/LST objects.

.EXAMPLE
    .\Create-ADObject.ps1 -JobID "123" -objectName "VM1" -objectDescription "Test VM" -vmOS "Windows" -Domain "example.com" -ouPath "OU=Test,DC=example,DC=com" -appType "VDI"

.EXAMPLE
    .\Create-ADObject.ps1 -JobID "456" -objectName "CLS001" -objectDescription "Cluster VM" -vmOS "Windows" -Domain "example.com" -ouPath "OU=Clusters,DC=example,DC=com" -appType "Cluster" -clusterNodes "NODE01,NODE02"

.NOTES
    File Name   : Create-ADObject.ps1
    Author      : Rudi van Zyl
    
    Version 1.5 - Added support for 'CLS' and 'LST' objects.
    Version 1.4 - Simplified further, removed unnecessary parameters and modules.
    Version 1.3 - Simplified and modernized the script.
    Version 1.2 - Refactored the script to be called instead of running on a schedule checking whether a job exists or not.
    Version 1.1 - Re-wrote Using AutoDeploy module
    Version 1.0.2 - Added issue_ref and issue_type to the API update.
    Version 1.0.1 - Added skipping AD object creation if this is a linux server.
    Version 1.0 - Base Script
#>
#Requires -Version 5
#Requires -Modules CredentialManager, ActiveDirectory

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$JobID,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$ObjectName,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$ObjectDescription,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [ValidateSet("Windows", "Linux")]
    [string]$VmOS,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$Domain,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$OuPath,
    
    [Parameter(Mandatory = $true)]
    [ValidateNotNullOrEmpty()]
    [string]$AppType,
    
    [Parameter(Mandatory = $false)]
    [string]$ClusterNodes
)

function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Success,
        
        [Parameter(Mandatory = $true)]
        [string]$Status,
        
        [Parameter(Mandatory = $true)]
        [string]$Message
    )
    
    $objectReturn = @{
        objectName   = $ObjectName
        Domain       = $Domain
        Comment      = $Script:UpdateComment
        ouCreatedIn  = $OuPath
        timeStamp    = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }
    
    $jsonResponse = @{
        success = $Success
        status  = $Status
        message = $Message
        data    = $objectReturn
    }
    
    return ($jsonResponse | ConvertTo-Json -Depth 3)
}

function Grant-ClusterNodePermissions {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [Microsoft.ActiveDirectory.Management.ADComputer]$AdObject,
        
        [Parameter(Mandatory = $true)]
        [string]$ClusterNodeList,
        
        [Parameter(Mandatory = $true)]
        [string]$DomainName
    )
    
    Write-Host "Object name starts with CLS/LST, granting full control to cluster nodes..." -ForegroundColor Yellow
    
    $nodeList = $ClusterNodeList -split ',' | ForEach-Object { $_.Trim() }
    
    foreach ($node in $nodeList) {
        if (-not [string]::IsNullOrWhiteSpace($node)) {
            try {
                $nodeAccount = Get-ADComputer -Identity $node -Server $DomainName -ErrorAction Stop
                $acl = Get-Acl -Path "AD:\$($AdObject.DistinguishedName)"
                
                $accessRule = New-Object System.DirectoryServices.ActiveDirectoryAccessRule(
                    $nodeAccount.SID,
                    [System.DirectoryServices.ActiveDirectoryRights]::GenericAll,
                    [System.Security.AccessControl.AccessControlType]::Allow
                )
                
                $acl.SetAccessRule($accessRule)
                
                Set-Acl -Path "AD:\$($AdObject.DistinguishedName)" -AclObject $acl
                
                Write-Host "Granted full control to $node on $($AdObject.Name)" -ForegroundColor Green
            }
            catch {
                Write-Warning "Failed to grant permissions to $node`: $($_.Exception.Message)"
            }
        }
    }
}

try {
    $Script:UpdateComment = ""
    $jobStatus = "IN_PROGRESS"
    $success = $true
    $accountExists = $false

    if ($VmOS -eq "Linux") {
        Write-Host "This is a Linux server - skipping AD account creation" -ForegroundColor DarkGreen
        $Script:UpdateComment = "Linux based server, no AD Object created."
        $jobStatus = "COMPLETED"
        return (New-JsonReturn -Success $success -Status $jobStatus -Message $Script:UpdateComment)
    }

    Write-Host "Getting credentials for domain: $Domain" -ForegroundColor Cyan
    $adCreds = if ($AppType -eq "VDI") { 
        Get-StoredCredential -Target "VDI" 
    }
    else {
        Get-StoredCredential -Target $Domain
    }

    if (-not $adCreds) {
        throw "Unable to retrieve stored credentials for target: $(if ($AppType -eq 'VDI') { 'VDI' } else { $Domain })"
    }

    Write-Host "Checking if computer object '$ObjectName' already exists in domain '$Domain'..." -ForegroundColor Cyan
    try {
        $existingAdObject = Get-ADComputer -Identity $ObjectName -Server $Domain -ErrorAction Stop
        if ($existingAdObject) {
            $warningMessage = "$ObjectName already exists in the $Domain domain; skipping the creation step."
            Write-Warning $warningMessage
            return (New-JsonReturn -Success "false" -Status "WARNING" -Message $warningMessage)
        }
    }
    catch [Microsoft.ActiveDirectory.Management.ADIdentityNotFoundException] {
        Write-Host "Computer name '$ObjectName' not found in the '$Domain' domain; proceeding with object creation." -ForegroundColor DarkGreen
        $accountExists = $false
    }
    catch [Microsoft.ActiveDirectory.Management.ADServerDownException] {
        $errorMessage = "Unable to reach a domain controller for $Domain"
        Write-Error $errorMessage
        return (New-JsonReturn -Success "false" -Status "ERROR" -Message $errorMessage)
    }
    catch {
        $errorMessage = "Error checking for existing AD object: $($_.Exception.Message)"
        Write-Error $errorMessage
        return (New-JsonReturn -Success "false" -Status "ERROR" -Message $errorMessage)
    }

    if (-not $accountExists) {
        Write-Host "Creating AD computer object '$ObjectName' in domain '$Domain'..." -ForegroundColor Cyan
        try {
            $newAdComputerParams = @{
                Name            = $ObjectName
                SAMAccountName  = $ObjectName
                Path            = $OuPath
                Description     = $ObjectDescription
                Enabled         = $true
                Credential      = $adCreds
                Server          = $Domain
                DNSHostName     = "$ObjectName.$Domain"
                OtherAttributes = @{ 'comment' = $JobID }
            }
            
            New-ADComputer @newAdComputerParams
            Write-Host "Successfully created AD computer object '$ObjectName'" -ForegroundColor Green
        }
        catch {
            $errorMessage = "Failed to create AD computer object: $($_.Exception.Message)"
            Write-Error $errorMessage
            return (New-JsonReturn -Success "false" -Status "ERROR" -Message $errorMessage)
        }

        Write-Host "Waiting for AD replication..." -ForegroundColor Cyan
        Start-Sleep -Seconds 10
    }

    try {
        $adObject = Get-ADComputer -Identity $ObjectName -Server $Domain -ErrorAction Stop
        
        if (($ObjectName.StartsWith("CLS", [System.StringComparison]::OrdinalIgnoreCase) -or 
             $ObjectName.StartsWith("LST", [System.StringComparison]::OrdinalIgnoreCase)) -and 
             -not [string]::IsNullOrWhiteSpace($ClusterNodes)) {
            
            Grant-ClusterNodePermissions -AdObject $adObject -ClusterNodeList $ClusterNodes -DomainName $Domain
        }
        
        $Script:UpdateComment = "AD Object $ObjectName created successfully in $Domain"
        $jobStatus = "COMPLETED"
        Write-Host "AD object '$ObjectName' created successfully in the '$Domain' domain" -ForegroundColor DarkGreen
    }
    catch {
        $errorMessage = "Failed to verify AD object creation: $($_.Exception.Message)"
        Write-Error $errorMessage
        return (New-JsonReturn -Success "false" -Status "ERROR" -Message $errorMessage)
    }
}
catch {
    $errorMessage = "Unexpected error: $($_.Exception.Message)"
    Write-Error $errorMessage
    return (New-JsonReturn -Success "false" -Status "ERROR" -Message $errorMessage)
}
finally {
    if (-not [string]::IsNullOrEmpty($Script:UpdateComment)) {
        New-JsonReturn -Success $success -Status $jobStatus -Message $Script:UpdateComment
    }
}
