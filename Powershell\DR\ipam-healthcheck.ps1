<#
    .SYNOPSIS
    IPAM Health Check for Disaster Recovery Testing.

    .DESCRIPTION
    This script validates IPAM service availability across primary and DR sites
    and logs results with timestamps. Enhanced with verbose logging, error details,
    response time measurements, and centralized logging.

    .PARAMETER Verbose
    Enable verbose logging with detailed information

    .PARAMETER Debug
    Enable debug logging with maximum detail including HTTP headers and response content

    .NOTES
    File Name: ipam_healthcheck.ps1
    Author: <PERSON><PERSON>
    Requires: Powershell 5.1
    Copyright (c) 2025 R<PERSON> van <PERSON>
    Version: 1.3 - 30/7/2025 - Enhanced logging capabilities
#>
#requires -Version 5
#requires -Modules PSPHPIPAM

param(
    [switch]$Verbose,
    [switch]$Debug
)

# Create output directory for evidence
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$evidenceDir = "$PSScriptRoot\Evidence\$timestamp"
New-Item -Path $evidenceDir -ItemType Directory -Force | Out-Null

# Create centralized master log file
$masterLogFile = "$evidenceDir\master_log.log"

$ipamBDC = 'srv009256.mud.internal.co.za'
$ipamCDC = 'srv009257.mud.internal.co.za'
$ipamHA = 'ipam.sanlam.co.za'

# Enhanced logging function with different log levels
function Write-LogEntry {
    param (
        [string]$Message,
        [ValidateSet("INFO", "WARN", "ERROR", "DEBUG")]
        [string]$Level = "INFO",
        [string]$LogFile = $masterLogFile,
        [switch]$ConsoleOutput
    )

    $logTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"
    $logEntry = "[$logTime] [$Level] $Message"

    # Write to log file
    $logEntry | Out-File -FilePath $LogFile -Append -Encoding utf8

    # Console output with color coding
    if ($ConsoleOutput -or $PSBoundParameters.ContainsKey('ConsoleOutput') -eq $false) {
        $color = switch ($Level) {
            "INFO" { "White" }
            "WARN" { "Yellow" }
            "ERROR" { "Red" }
            "DEBUG" { "Gray" }
        }

        if ($Level -eq "DEBUG" -and -not $Debug) {
            # Skip debug output unless debug mode is enabled
            return
        }

        Write-Host $logEntry -ForegroundColor $color
    }
}

# Enhanced function to test web service with detailed logging
function Test-IPAMService {
    param (
        [string]$Name,
        [string]$Url
    )

    $logFile = "$evidenceDir\${Name}_results.log"
    $testTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

    Write-LogEntry "Starting test for $Name at $Url" -Level "INFO"

    # Initialize variables for enhanced logging
    $responseTime = $null
    $contentLength = $null
    $serverHeader = $null
    $errorDetails = $null
    $httpHeaders = @{}

    try {
        # Measure response time
        $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()

        if ($Debug) {
            Write-LogEntry "Sending HTTP request to $Url" -Level "DEBUG"
        }

        $response = Invoke-WebRequest -Uri $Url -ErrorAction Stop -TimeoutSec 30
        $stopwatch.Stop()
        $responseTime = $stopwatch.ElapsedMilliseconds

        $statusCode = $response.StatusCode
        $status = if ($statusCode -eq 200) { "ONLINE" } else { "ERROR" }

        # Extract detailed HTTP response information
        $contentLength = $response.Headers['Content-Length']
        $serverHeader = $response.Headers['Server']

        # Store all headers for debug logging
        if ($Debug) {
            foreach ($header in $response.Headers.GetEnumerator()) {
                $httpHeaders[$header.Key] = $header.Value -join ', '
            }
        }

        Write-LogEntry "Successfully connected to $Name - Status: $statusCode, Response Time: ${responseTime}ms" -Level "INFO"

        if ($Verbose) {
            Write-LogEntry "Content-Length: $contentLength, Server: $serverHeader" -Level "INFO"
        }

        if ($Debug) {
            Write-LogEntry "Full HTTP Headers: $($httpHeaders | ConvertTo-Json -Compress)" -Level "DEBUG"
            Write-LogEntry "Response content preview: $($response.Content.Substring(0, [Math]::Min(200, $response.Content.Length)))" -Level "DEBUG"
        }
    }
    catch {
        if ($stopwatch) { $stopwatch.Stop() }
        $responseTime = if ($stopwatch) { $stopwatch.ElapsedMilliseconds } else { 0 }
        $status = "OFFLINE"
        $statusCode = 0

        # Enhanced error logging
        $errorDetails = @{
            Exception = $_.Exception.Message
            FullyQualifiedErrorId = $_.FullyQualifiedErrorId
            CategoryInfo = $_.CategoryInfo.ToString()
            InvocationInfo = @{
                ScriptLineNumber = $_.InvocationInfo.ScriptLineNumber
                OffsetInLine = $_.InvocationInfo.OffsetInLine
            }
        }

        Write-LogEntry "Failed to connect to $Name - Error: $($_.Exception.Message)" -Level "ERROR"

        if ($Verbose) {
            Write-LogEntry "Error Category: $($_.CategoryInfo.Category), Reason: $($_.CategoryInfo.Reason)" -Level "WARN"
        }

        if ($Debug) {
            Write-LogEntry "Full error details: $($errorDetails | ConvertTo-Json -Depth 3)" -Level "DEBUG"
        }
    }

    # Enhanced log entry for individual service log
    $detailedLogEntry = @"
[$testTime] $Name ($Url): $status
  - HTTP Status: $statusCode
  - Response Time: ${responseTime}ms
  - Content-Length: $contentLength
  - Server: $serverHeader
"@

    if ($errorDetails) {
        $detailedLogEntry += "`n  - Error: $($errorDetails.Exception)"
    }

    $detailedLogEntry | Out-File -FilePath $logFile -Append -Encoding utf8

    # Log to master log file
    Write-LogEntry "$Name test completed - Status: $status, Response Time: ${responseTime}ms" -Level "INFO" -ConsoleOutput:$false

    return [PSCustomObject]@{
        Name = $Name
        Url = $Url
        Status = $status
        StatusCode = $statusCode
        TestTime = $testTime
        ResponseTime = $responseTime
        ContentLength = $contentLength
        ServerHeader = $serverHeader
        ErrorDetails = $errorDetails
        HttpHeaders = $httpHeaders
        LogFile = $logFile
    }
}

# Get current user information
$currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
$computerName = $env:COMPUTERNAME

# Initialize logging
Write-LogEntry "=== IPAM DR Health Check Started ===" -Level "INFO"
Write-LogEntry "Executed by: $currentUser on computer: $computerName" -Level "INFO"
Write-LogEntry "Test Parameters - Verbose: $Verbose, Debug: $Debug" -Level "INFO"
Write-LogEntry "Evidence Directory: $evidenceDir" -Level "INFO"

# Test IPAM services
$services = @(
    @{Name="Highly-Available IPAM"; Url="https://$ipamHA/ipam/"},
    @{Name="BDC IPAM"; Url="http://$ipamBDC/ipam/"},
    @{Name="CDC IPAM"; Url="http://$ipamCDC/ipam/"}
)

Write-LogEntry "Testing $($services.Count) IPAM services" -Level "INFO"

$results = @()
foreach ($service in $services) {
    Write-LogEntry "--- Testing $($service.Name) ---" -Level "INFO"
    $result = Test-IPAMService -Name $service.Name -Url $service.Url
    $results += $result

    # Enhanced console display with response time
    $statusColor = if ($result.Status -like "ONLINE*") { "Green" } else { "Red" }
    $displayMessage = "[$($result.TestTime)] $($result.Name): $($result.Status)"

    if ($null -ne $result.ResponseTime) {
        $displayMessage += " (${result.ResponseTime}ms)"
    }

    Write-Host $displayMessage -ForegroundColor $statusColor
}

# Generate enhanced summary report
Write-LogEntry "Generating summary reports" -Level "INFO"

$summaryFile = "$evidenceDir\summary_report.html"
$htmlContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>IPAM DR Health Check - $timestamp</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #0066cc; }
        h2 { color: #0066cc; margin-top: 30px; }
        table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .online { color: green; font-weight: bold; }
        .offline { color: red; font-weight: bold; }
        .error { color: orange; font-weight: bold; }
        .summary-stats { background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .response-time { text-align: right; }
    </style>
</head>
<body>
    <h1>IPAM DR Health Check Results</h1>
    <p>Test conducted on: $timestamp</p>
    <p>Executed by: $currentUser on computer: $computerName</p>

    <div class="summary-stats">
        <h2>Summary Statistics</h2>
        <p>Total Services Tested: $($results.Count)</p>
        <p>Online Services: $($results | Where-Object {$_.Status -eq 'ONLINE'} | Measure-Object | Select-Object -ExpandProperty Count)</p>
        <p>Offline Services: $($results | Where-Object {$_.Status -eq 'OFFLINE'} | Measure-Object | Select-Object -ExpandProperty Count)</p>
        <p>Error Services: $($results | Where-Object {$_.Status -eq 'ERROR'} | Measure-Object | Select-Object -ExpandProperty Count)</p>
        <p>Average Response Time: $(if ($results | Where-Object {$null -ne $_.ResponseTime}) { [math]::Round(($results | Where-Object {$null -ne $_.ResponseTime} | Measure-Object -Property ResponseTime -Average).Average, 2) } else { 'N/A' })ms</p>
    </div>

    <h2>Detailed Results</h2>
    <table>
        <tr>
            <th>Service</th>
            <th>URL</th>
            <th>Status</th>
            <th>HTTP Status</th>
            <th>Response Time (ms)</th>
            <th>Content Length</th>
            <th>Server</th>
            <th>Test Time</th>
        </tr>
"@

foreach ($result in $results) {
    $statusClass = switch ($result.Status) {
        "ONLINE" { "online" }
        "OFFLINE" { "offline" }
        "ERROR" { "error" }
        default { "offline" }
    }

    $responseTimeDisplay = if ($null -ne $result.ResponseTime) { $result.ResponseTime } else { "N/A" }
    $contentLengthDisplay = if ($result.ContentLength) { $result.ContentLength } else { "N/A" }
    $serverDisplay = if ($result.ServerHeader) { $result.ServerHeader } else { "N/A" }

    $htmlContent += @"
        <tr>
            <td>$($result.Name)</td>
            <td>$($result.Url)</td>
            <td class="$statusClass">$($result.Status)</td>
            <td>$($result.StatusCode)</td>
            <td class="response-time">$responseTimeDisplay</td>
            <td>$contentLengthDisplay</td>
            <td>$serverDisplay</td>
            <td>$($result.TestTime)</td>
        </tr>
"@
}

$htmlContent += @"
    </table>

    <h2>Log Files Generated</h2>
    <ul>
        <li><strong>Master Log:</strong> master_log.log</li>
"@

foreach ($result in $results) {
    $logFileName = Split-Path $result.LogFile -Leaf
    $htmlContent += "        <li><strong>$($result.Name):</strong> $logFileName</li>`n"
}

$htmlContent | Out-File -FilePath $summaryFile -Encoding utf8

# Generate final summary statistics
$onlineCount = ($results | Where-Object {$_.Status -eq 'ONLINE'}).Count
$offlineCount = ($results | Where-Object {$_.Status -eq 'OFFLINE'}).Count
$errorCount = ($results | Where-Object {$_.Status -eq 'ERROR'}).Count
$totalCount = $results.Count

Write-LogEntry "=== IPAM DR Health Check Completed ===" -Level "INFO"
Write-LogEntry "Summary: $onlineCount/$totalCount services online, $offlineCount offline, $errorCount errors" -Level "INFO"
Write-LogEntry "Evidence saved to: $evidenceDir" -Level "INFO"
Write-LogEntry "Master log file: $masterLogFile" -Level "INFO"
Write-LogEntry "HTML report: $summaryFile" -Level "INFO"

Write-Host "`n=== DR Test Completed ===" -ForegroundColor Cyan
Write-Host "Results: $onlineCount/$totalCount services online" -ForegroundColor $(if ($onlineCount -eq $totalCount) { "Green" } else { "Yellow" })
Write-Host "Evidence saved to: $evidenceDir" -ForegroundColor Cyan
Write-Host "Master log: $masterLogFile" -ForegroundColor Cyan
Write-Host "Summary report: $summaryFile" -ForegroundColor Cyan

if ($Verbose -or $Debug) {
    Write-Host "`nEnhanced logging was enabled. Check individual service logs for detailed information." -ForegroundColor Yellow
}
