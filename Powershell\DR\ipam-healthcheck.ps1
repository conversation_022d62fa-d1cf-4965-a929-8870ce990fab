<#
    .SYNOPSIS
    IPAM Health Check for Disaster Recovery Testing.

    .DESCRIPTION
    This script validates IPAM service availability across primary and DR sites
    and logs results with timestamps.

    .NOTES
    File Name: ipam_healthcheck.ps1
    Author: <PERSON><PERSON>
    Requires: Powershell 5.1
    Copyright (c) 2024 <PERSON><PERSON>
    Version: 1.2 - Current Date
#>
#requires -Version 5

# Create output directory for evidence
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm-ss"
$evidenceDir = "$PSScriptRoot\Evidence\$timestamp"
New-Item -Path $evidenceDir -ItemType Directory -Force | Out-Null
$ipamBDC = 'srv009256.mud.internal.co.za'
$ipamCDC = 'srv009257.mud.internal.co.za'
$ipamHA = 'ipam.sanlam.co.za'

# Function to test web service
function Test-IPAMService {
    param (
        [string]$Name,
        [string]$Url
    )
    
    $logFile = "$evidenceDir\${Name}_results.log"
    $testTime = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    try {
        $response = Invoke-WebRequest -Uri $Url -ErrorAction Stop
        $statusCode = $response.StatusCode
        $status = if ($statusCode -eq 200) { "ONLINE" } else { "ERROR" }
    } 
    catch {
        $status = "OFFLINE"
        $statusCode = 0  # Set to 0 when connection fails
    }
    
    # Log results
    "[$testTime] $Name ($Url): $status (HTTP Status: $statusCode)" | Out-File -FilePath $logFile -Append
    
    return [PSCustomObject]@{
        Name = $Name
        Url = $Url
        Status = $status
        StatusCode = $statusCode
        TestTime = $testTime
        LogFile = $logFile
    }
}

# Test IPAM services
$services = @(
    @{Name="Highly-Available IPAM"; Url="https://$ipamHA/ipam/"},
    @{Name="BDC IPAM"; Url="http://$ipamBDC/ipam/"},
    @{Name="CDC IPAM"; Url="http://$ipamCDC/ipam/"}
)

$results = @()
foreach ($service in $services) {
    $result = Test-IPAMService -Name $service.Name -Url $service.Url
    $results += $result
    
    # Display results in console
    Write-Host "[$($result.TestTime)] $($result.Name): $($result.Status)" -ForegroundColor $(if ($result.Status -like "ONLINE*") { "Green" } else { "Red" })
}

# Generate summary report
$summaryFile = "$evidenceDir\summary_report.html"
$htmlContent = @"
<!DOCTYPE html>
<html>
<head>
    <title>IPAM DR Health Check - $timestamp</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #0066cc; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .online { color: green; font-weight: bold; }
        .offline { color: red; font-weight: bold; }
    </style>
</head>
<body>
    <h1>IPAM DR Health Check Results</h1>
    <p>Test conducted on: $timestamp</p>
    <table>
        <tr>
            <th>Service</th>
            <th>URL</th>
            <th>Status</th>
            <th>HTTP Status</th>
            <th>Test Time</th>
        </tr>
"@

foreach ($result in $results) {
    $statusClass = if ($result.Status -like "ONLINE*") { "online" } else { "offline" }
    $htmlContent += @"
        <tr>
            <td>$($result.Name)</td>
            <td>$($result.Url)</td>
            <td class="$statusClass">$($result.Status)</td>
            <td>$($result.StatusCode)</td>
            <td>$($result.TestTime)</td>
        </tr>
"@
}

$htmlContent += @"
    </table>
</body>
</html>
"@

$htmlContent | Out-File -FilePath $summaryFile -Encoding utf8

Write-Host "`nDR Test completed. Evidence saved to: $evidenceDir" -ForegroundColor Cyan
Write-Host "Summary report: $summaryFile" -ForegroundColor Cyan
